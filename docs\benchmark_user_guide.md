# Guia de Uso para Usuários de Negócio — Benchmark de Modelos

## Objetivo

Apoiar a interpretação dos resultados do arquivo `model_benchmark.csv` e conduzir a escolha do melhor modelo para implantação, relacionando métricas técnicas ao impacto de negócio.

## Como ler o `model_benchmark.csv`

Cada linha representa um modelo testado. As colunas principais são:
- `CV_WAPE`, `CV_sMAPE`, `CV_MAE`, `CV_RMSE`: desempenho médio em validação cruzada temporal.
- `HOLDOUT_WAPE`, `HOLDOUT_sMAPE`, `HOLDOUT_MAE`, `HOLDOUT_RMSE`: desempenho no período final (dias mais recentes), simulando uso real.
- Quanto menores os valores, melhor o desempenho (menor erro).

Arquivos complementares:
- `baseline_holdout_metrics.csv`: desempenho de baselines (média móvel 7, sazonal, naive) no mesmo período de teste.
- `model_recommendation.txt`: recomendação automatizada do melhor modelo.

## Métricas no contexto de negócio

- WAPE (Weighted APE): mede erro relativo ponderado pelo volume. Excelente para comparar lojas de tamanhos diferentes. Ex.: WAPE=0,20 indica erro médio de 20% do total previsto.
- sMAPE: erro percentual simétrico, útil quando há muitos valores baixos.
- MAE (Erro Absoluto Médio) e RMSE: erros em unidades monetárias (centavos), úteis para estimar impacto financeiro agregado.

## Seleção de modelo

1. Prefira menor `HOLDOUT_WAPE` (período recente) – reflete o cenário de uso.
2. Use `CV_*` para verificar estabilidade (evitar “sorte” no holdout).
3. Compare com baselines: busque `uplift_vs_baseline_%` positivo e relevante.
4. Caso de uso regional: confira segmentações (plots por UF/Tipo_PDV) para entender onde o modelo performa melhor.

## Boas práticas

- Não implante modelos com `HOLDOUT_WAPE` pior que baseline sazonal (se disponível).
- Verifique consistência temporal (diferenças grandes entre CV e Holdout podem indicar sobreajuste).
- Avalie métricas executivas: acurácia em Top/Bottom 20% de lojas e erro no ticket médio.

## Troubleshooting comum

- “Poucos dados por loja”: componentes de validação podem reduzir dobras. Amplie período histórico se possível.
- “Plots não gerados”: pode faltar `matplotlib/seaborn/shap`. A análise numérica ainda é válida.
- “Sem SHAP”: a biblioteca `shap` pode não estar instalada; o recomendador ainda funciona sem explicabilidade visual.

## Ações recomendadas pós‑análise

- Validar o melhor modelo em um piloto (praças selecionadas) monitorando WAPE e uplift vs baseline.
- Revisitar features regionais e sazonais conforme learnings locais.
- Incorporar métricas executivas em rotinas de BI/Dashboards.



## Atualização — Macro‑região

- Novo gráfico: `segmentation_wape_by_macroregiao.png (gerado apenas quando h� modelo treinado e holdout v�lido)`
- Nova tabela: `metrics_by_macroregiao.csv`
- Interpretação: comparar WAPE entre regiões para identificar onde o modelo generaliza melhor ou exige ajustes locais.


## Interpretação do TMMN

- Definição (simplificada): compara o ticket médio da loja ao benchmark da macro‑região e ajusta por saturação (densidade de lojas) e poder de compra proxy.
- Leitura:
  - TMMN > 1: ticket marginal acima do esperado (após ajustes) — potencial de expansão/cross-sell.
  - TMMN ≈ 1: em linha com a região.
  - TMMN < 1: abaixo do esperado — avaliar mix, promoções, pricing.
- Artefatos:
  - `segmentation_tmmn_by_macroregiao.png (gerado quando TMMN dispon�vel e df n�o vazio)`
  - `tmmn_by_macroregiao.csv`

## Visualizações experimentais (interativas)

- O sistema gera versões interativas em HTML (quando bibliotecas disponíveis) para comparação com os PNG tradicionais:
  - Plotly: `*_plotly.html`
  - Altair: `*_altair.html`
  - Bokeh: `*_bokeh.html`
- Localização: `reports/2025-08-15/plots/model_comparison/experimental/`
- Exemplos:
  - `compare_wape_plotly.html`, `compare_wape_altair.html`, `compare_wape_bokeh.html`
  - `uplift_vs_baseline_baseline_naive_plotly.html` (varia conforme baseline escolhida)
  - `segmentation_wape_by_macroregiao_*`, `segmentation_tmmn_by_macroregiao_*`


## Navegação — Novas Visualizações Comparativas

Os seguintes artefatos são gerados quando há dados suficientes (folds de CV e/ou holdout válido). Utilize-os para aprofundar a análise e selecionar modelos com equilíbrio entre acurácia e robustez.

1) Matrizes de performance por família e métrica (PNG)
- Local: reports/2025-08-15/plots/model_comparison/matrix_cv_{wape|smape|mae|rmse}.png
- Leitura: identifique famílias/modelos com menor erro médio (cores/valores menores). Útil para triagem rápida por família.

2) Melhor modelo por família (PNG)
- Local: reports/2025-08-15/plots/model_comparison/best_by_family_{holdout_wape|cv_wape}.png
- Leitura: aponta o “vencedor” de cada família; combine com métricas CV para avaliar estabilidade.

3) Distribuições de CV por família (PNG)
- Local: reports/2025-08-15/plots/model_comparison/cv_distribution_box_{wape|smape|mae|rmse}.png e cv_distribution_violin_*.png
- Leitura: avalie dispersão e caudas; famílias com boxplots compactos tendem a maior previsibilidade.

4) Testes de significância entre Top‑3 (PNG + CSV)
- Locais:
  - Gráfico: reports/2025-08-15/plots/model_comparison/cv_significance_top3.png
  - Tabela: reports/2025-08-15/tables/cv_top_models_significance.csv
- Leitura: p‑values < 0,05 sugerem diferença estatisticamente significativa de WAPE entre pares.

5) Comparação de importâncias/coeficientes (PNG)
- Local: reports/2025-08-15/plots/model_comparison/feature_importance_comparison.png
- Leitura: destaca drivers de erro por modelo; útil para decisões de negócio e auditoria técnica.

6) Versões interativas (HTML, quando disponíveis)
- Local: reports/2025-08-15/plots/model_comparison/experimental/
- Exemplos: compare_{metric}_{plotly|altair|bokeh}.html, uplift_vs_baseline_{baseline}_{plotly|altair|bokeh}.html
- Uso: preferir HTMLs para exploração (tooltips/zoom), PNGs para relatórios estáticos.

Notas (datasets curtos ~5 dias)
- É esperado que alguns desses artefatos não apareçam; isso não invalida o benchmark numérico.
- Para habilitar todos os gráficos, amplie o período histórico (recomendação: ≥ 60–90 dias).

Dicas de uso:
- Passe o mouse para ver tooltips com contexto de negócio; use zoom/pan conforme disponível.
- Use os PNG para relatórios estáticos e os HTML para exploração.

## Troubleshooting (datasets curtos)

- Com 5 dias, janelas móveis e normalizações ficam instáveis; use leituras qualitativas.
- Amplie a janela temporal para medidas mais estáveis (≥ 60–90 dias recomendados).

