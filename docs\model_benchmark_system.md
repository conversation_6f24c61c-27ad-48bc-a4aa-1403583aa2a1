# Sistema Automatizado de Benchmark de Modelos

## Visão Geral da Arquitetura

- Entrada: `data/clean/cleaned.csv` (colunas mínimas: `data`, `id_loja`, `valor`; opcionais: `qtd`, `uf`, `cidade`, `Tipo_PDV`).
- Motor de benchmark: `scripts/automated_model_benchmark.py` (função `run_model_comparison`).
- Pipeline principal integra benchmark: `scripts/final_analysis.py` (chamada após baselines).
- Saídas:
  - Tabelas: `reports/2025-08-15/tables/model_benchmark.csv`, `baseline_holdout_metrics.csv`, `model_recommendation.txt`.
  - Plots: `reports/2025-08-15/plots/model_comparison/*.png` (comparativos, uplift, segmentações, SHAP).
  - Dataset de features: `data/processed/features_engineered.csv`.

## Pipeline de Feature Engineering (anti‑vazamento)

1. Ordenação por `id_loja`, `data`.
2. Atributos temporais: `dow`, `month`, `year`, `is_weekend`, `store_age_days`.
3. Defasagens de `valor`: lags (1,2,3,7,14) e médias móveis defasadas (7,14) com `shift(1)` antes do `rolling`.
4. Defasagens de `qtd`: `qtd_lag_1`, `qtd_lag_7` (nunca usar `qtd` do mesmo dia).
5. Codificação cíclica: `dow_sin/cos`, `month_sin/cos`.
6. Categóricas de baixa cardinalidade: one‑hot para `uf`, `Tipo_PDV` (sem alvo).
7. Categóricas de alta cardinalidade (`id_loja`, `cidade`): target encoding com K‑fold temporal (fit apenas no treino da dobra; aplicado em validação/holdout).

Salvaguardas anti‑leakage:
- Todas as janelas de rolling usam `shift(1)`.
- Splits temporais por loja (holdout adaptativo: últimos 20% dias, máx. 14, mín. 1).
- Target encoding calculado exclusivamente no conjunto de treino de cada dobra.

## Modelos e Hiperparâmetros

- LightGBM (se disponível): `n_estimators` ∈ {50,100,200}, `learning_rate` ∈ {0.05,0.1,0.2}, `max_depth` ∈ {3,5,7}.
- RandomForest: `n_estimators` ∈ {100,200}, `max_depth` ∈ {None,5,10}, `min_samples_leaf` ∈ {1,3,5}.
- XGBoost (se disponível): análogo ao LightGBM com `objective=reg:squarederror`.
- Baselines: naive (último valor), média móvel 7, sazonal semanal.

Justificativa:
- Grades pequenas, porém suficientes para capturar diferentes vieses/variâncias.
- Modelos de árvore são robustos a não‑linearidades e interações, adequados a dados tabulares.

## Metodologia de Validação

- Cross‑validation temporal: `TimeSeriesSplit` com 2–3 dobras (adaptativa ao número de amostras).
- Holdout temporal final por loja: últimos 20% de dias (limites: 1–14 dias).
- Métricas reportadas: WAPE, sMAPE, MAE, RMSE (CV e Holdout), além de baselines em holdout.

## Estrutura de Arquivos e Saídas

- Tabelas:
  - `model_benchmark.csv`: resultados agregados (CV e Holdout) por modelo.
  - `baseline_holdout_metrics.csv`: métricas por baseline, por loja, no holdout.
  - `model_recommendation.txt`: melhor modelo sugerido.
- Plots (em `plots/model_comparison/`):
  - `compare_wape.png`, `compare_smape.png`, `compare_mae.png`, `compare_rmse.png`.
  - `uplift_vs_baseline_*.png`.
  - `segmentation_wape_by_uf.png`, `segmentation_wape_by_Tipo_PDV.png`.
  - `shap_summary.png` (se `shap` estiver disponível).
- Versões interativas (em `plots/model_comparison/experimental/`, se libs disponíveis):
  - Plotly: `*_plotly.html`
  - Altair: `*_altair.html`
  - Bokeh: `*_bokeh.html`

### Índice de Visualizações (referências)

Novas visualizações comparativas foram adicionadas ao final da rotina de benchmark. Todas respeitam fallbacks para cenários de dados insuficientes.

- Matrizes de performance por família e métrica (PNG)
  - reports/2025-08-15/plots/model_comparison/matrix_cv_{wape|smape|mae|rmse}.png
  - Requisitos: métricas de CV disponíveis (res_df não vazio nas colunas CV_*).
  - Interpretação: comparação global entre famílias/modelos.

- Melhor modelo por família (PNG)
  - reports/2025-08-15/plots/model_comparison/best_by_family_{holdout_wape|cv_wape}.png
  - Requisitos: coluna de ordenação existente (HOLDOUT_WAPE preferencial; senão CV_WAPE).
  - Interpretação: seleção do “vencedor” em cada família.

- Distribuições de CV (box/violin) por família (PNG)
  - reports/2025-08-15/plots/model_comparison/cv_distribution_box_{wape|smape|mae|rmse}.png
  - reports/2025-08-15/plots/model_comparison/cv_distribution_violin_{wape|smape|mae|rmse}.png
  - Requisitos: arquivos de dobras por modelo (reports/2025-08-15/tables/cv_folds_{model}.csv).
  - Interpretação: estabilidade/variabilidade do erro por família.

- Testes de significância Top‑3 (PNG + CSV)
  - PNG: reports/2025-08-15/plots/model_comparison/cv_significance_top3.png
  - CSV: reports/2025-08-15/tables/cv_top_models_significance.csv
  - Requisitos: WAPE por dobra para pelo menos 3 modelos.
  - Interpretação: p-values de Mann‑Whitney (two-sided) com referência 0,05.

- Comparação de importâncias/coeficientes (PNG)
  - reports/2025-08-15/plots/model_comparison/feature_importance_comparison.png
  - Requisitos: modelos interpretáveis válidos (árvores com feature_importances_, lineares com coef_).
  - Interpretação: drivers de erro por modelo e auditoria técnica.

- Comparativos e Uplift (PNG + HTML interativo)
  - PNGs: compare_{metric}.png; uplift_vs_baseline_{baseline}.png
  - HTMLs: experimental/compare_{metric}_{plotly|altair|bokeh}.html; experimental/uplift_vs_baseline_{baseline}_{plotly|altair|bokeh}.html
  - Requisitos: métricas agregadas disponíveis; libs instaladas para HTML.

Observação (dataset de 5 dias)
- É esperado que alguns artefatos não sejam gerados por insuficiência de dados. A execução permanece íntegra graças aos fallbacks.

- Dados processados: `data/processed/features_engineered.csv`.

## Integração com `final_analysis.py`

- O pipeline chama `run_model_comparison(df_clean, reports/2025-08-15)` após os baselines.
- Em caso de falha (poucos dados ou dependências ausentes), o pipeline segue e loga um aviso.

## Observações

- Para dados com poucos dias por loja, as dobras podem ser reduzidas para manter consistência.
- Recomendado ampliar janela temporal para melhor robustez estatística.



## Atualização — Macro‑região e Proxies Demográficos

- Adicionadas features de ticket médio e proxies demográficos (sem vazamento):
  - ticket, ticket_medio_loja, ticket_medio_loja_lag_7, ticket_relativo_vs_media_uf
  - densidade_lojas_uf, receita_per_capita_estimada, ticket_normalizado_densidade
- Implementado mapeamento UF→macro‑região e inclusão em one‑hot.
- Novos artefatos:
  - plots/model_comparison/segmentation_wape_by_macroregiao.png (se dispon�vel)
  - plots/model_comparison/segmentation_tmmn_by_macroregiao.png (se dispon�vel)
  - tables/metrics_by_macroregiao.csv (se dispon�vel)
  - tables/tmmn_by_macroregiao.csv (se dispon�vel)

