# Estrutura do Projeto InfoPepper

Este documento descreve a organização de arquivos e pastas do projeto InfoPepper após a reorganização estrutural.

## Visão Geral

O projeto foi reorganizado seguindo as melhores práticas de estruturação de projetos de ciência de dados, com separação clara entre dados, código, documentação e resultados.

## Estrutura Detalhada

### 📁 assets/
Recursos visuais organizados por categoria:

- **images/**: Imagens gerais como histogramas, gráficos e logo da Inteli
- **personas/**: <PERSON><PERSON><PERSON> de personas (<PERSON>, <PERSON>, <PERSON>) e suas jornadas
- **diagrams/**: Diagramas técnicos e fluxos de metodologia
- **business/**: Análises de negócio (Canvas, SWOT, 5 Forças, matriz de riscos)

### 📁 data/
Dados organizados por estágio de processamento:

- **raw/**: Dados originais não processados (Excel, CSV originais)
- **processed/**: Dados limpos e processados (cleaned.csv, cleaned.parquet)
- **external/**: Dados externos de APIs ou outras fontes

### 📁 src/
Código fonte modularizado por funcionalidade:

- **data/**: Scripts de processamento e limpeza de dados
  - `io_utils.py`: Utilitários de entrada/saída
  - `features.py`: Engenharia de features
- **models/**: Scripts de modelagem e algoritmos
  - `baselines.py`: Modelos baseline
- **visualization/**: Scripts de visualização (futuro)
- **utils/**: Utilitários gerais
  - `analysis_stats.py`: Análises estatísticas
  - `metrics.py`: Métricas de avaliação
  - `geo_utils.py`: Utilitários geográficos

### 📁 scripts/
Scripts de automação e análise:

- `final_analysis.py`: Script de análise final
- `validate_structure.py`: Validação da estrutura
- `generate_corr.py`: Geração de correlações
- `generate_pipeline_diagram.py`: Geração de diagramas

### 📁 notebooks/
Jupyter notebooks para análise exploratória:

- `chilli_beans_analysis.ipynb`: Notebook principal
- `chilli_beans_analysis.html`: Export HTML do notebook
- Outros notebooks de análise específica

### 📁 reports/
Resultados organizados:

- **figures/**: Gráficos e visualizações (heatmaps, rankings, etc.)
- **tables/**: Tabelas e métricas em formato CSV
- **exports/**: Relatórios finais exportados

### 📁 docs/
Documentação técnica:

- `data_dictionary.md`: Dicionário de dados
- `project_structure.md`: Este documento

### 📁 documents/
Documentos do projeto:

- **extras/**: Documentos complementares
  - `documentacao.md`: Documentação adicional

### 📁 tests/
Testes unitários (estrutura preparada para futuro desenvolvimento)

### 📁 config/
Arquivos de configuração (estrutura preparada para futuro desenvolvimento)

### 📁 tools/
Ferramentas auxiliares:

- `build_notebook.py`: Construção de notebooks

## Benefícios da Nova Estrutura

1. **Organização Clara**: Separação lógica entre diferentes tipos de arquivos
2. **Escalabilidade**: Estrutura preparada para crescimento do projeto
3. **Manutenibilidade**: Fácil localização e manutenção de arquivos
4. **Padrões**: Seguimento de convenções da comunidade de ciência de dados
5. **Modularidade**: Código organizado em módulos reutilizáveis

## Migração de Caminhos

### Principais mudanças de caminhos:

- `data/clean/` → `data/processed/`
- `reports/2025-08-15/plots/` → `reports/figures/`
- Scripts da raiz → `scripts/`
- Scripts de `assets/` → `scripts/`
- Arquivos do `src/` reorganizados em subpastas

### Atualizações necessárias:

Se você tem scripts ou notebooks que referenciam os caminhos antigos, atualize para:

```python
# Antigo
data_path = "data/clean/cleaned.parquet"
plot_path = "reports/2025-08-15/plots/"

# Novo
data_path = "data/processed/cleaned.parquet"
plot_path = "reports/figures/"
```

## Próximos Passos

1. Atualizar imports nos notebooks para refletir a nova estrutura do `src/`
2. Criar testes unitários na pasta `tests/`
3. Adicionar arquivos de configuração em `config/`
4. Documentar APIs dos módulos em `src/`
