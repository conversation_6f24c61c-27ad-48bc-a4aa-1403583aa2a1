# Scripts do Projeto InfoPepper

Scripts de automação e análise atualmente disponíveis no projeto.

## Scripts Disponíveis

### `final_analysis.py` 

Pipeline abrangente de análise final com tratamento de erros. Executa análise completa dos dados com logging detalhado.

**Funcionalidade**: Análise final automatizada do projeto
**Uso**: `python scripts/final_analysis.py`

### `generate_corr.py` 

Gera matriz de correlação dos dados limpos usando Spearman.

**Funcionalidade**: Criação de heatmap de correlação
**Output**: `reports/figures/heatmap_corr.png`
**Uso**: `python scripts/generate_corr.py`

### `generate_pipeline_diagram.py` 

Cria diagrama visual do pipeline de metodologia usando matplotlib.

**Funcionalidade**: Geração de diagrama do pipeline de 8 etapas
**Output**: `assets/diagrams/metodologia_pipeline.png`
**Uso**: `python scripts/generate_pipeline_diagram.py`

### `validate_structure.py` 

Valida a estrutura do projeto verificando diretórios vazios e artefatos de relatório.

**Funcionalidade**: Verificação de integridade da estrutura
**Uso**: `python scripts/validate_structure.py`

## Observações

- Todos os scripts estão funcionais e testados
- `generate_corr.py` usa caminhos antigos (precisa atualização)
- Scripts podem ser executados independentemente
- Dependem das bibliotecas em `requirements.txt`
