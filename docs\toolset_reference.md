# Technical Toolset Reference (Chilli Beans Benchmark System)

This document catalogs the custom Python utilities, classes, and functions implemented in the automated model benchmark system. It is a technical reference intended to support unit test planning and maintenance. It is separate from business/user-facing documentation.

## Metrics
- wape(y_true, y_pred): Weighted Absolute Percentage Error with small-epsilon stabilization.
- smape(y_true, y_pred): Symmetric MAPE with small-epsilon stabilization.
- TMMN: Implemented inside feature engineering/analysis (see feature pipeline). Normalized marginal ticket; relies on macro-regional averages and density normalization.

## Target Encoding (Leak-safe)
- _target_encode(train_cat, train_y, apply_cat, min_samples=5, smoothing=10.0)
  - Computes smoothed means per category from the training partition only; applies to apply_cat.
  - Smoothing weights down rare categories; uses global mean back-off.
  - Used via make_te_features within run_model_comparison.

## Unsupervised Feature Augmenters
- KMeansLabeler(n_clusters=3, random_state=42)
  - fit: trains KMeans on X; transform: appends cluster label column to X.
- DBSCANApprox<PERSON>abeler(eps=0.5, min_samples=5)
  - fit: trains DBSCAN; stores labels and nearest-neighbor model for inference; transform: assigns nearest training label to new points and appends as a column.
- IsolationScore(n_estimators=100, random_state=42)
  - fit: trains IsolationForest; transform: appends anomaly score column (negated so that higher=more anomalous).

## Feature Engineering (No Leakage)
- _add_time_features(df): temporal features (dow, month, year, is_weekend, age_since_open, day_index, cyclical sin/cos).
- _add_rolling_lags(df): per-store shift(1) then rolling means and lags to avoid leakage.
- _add_ticket_and_demographics(df): ticket médio features and demographics.
- _add_macroregiao(df): UF→macro-região mapping and one-hot/label convenience columns.
- make_te_features(df_train, y_train, df_apply): orchestrates target encoding for multiple categorical columns.

## Anti-Leakage & Validation Utilities
- Temporal splitting per loja; holdout is the most recent window per loja when available.
- TimeSeriesSplit for CV; target encoding and augmenters fit only on training folds.
- All rolling windows created with shift(1) before aggregation.

## Model Grid & Benchmarking
- _model_grid(): returns list[(name, estimator)] including:
  - Tree-based: RandomForest, optional LightGBM/XGBoost, GradientBoosting
  - Linear: Ridge, Lasso
  - Kernel: SVR (RBF, Poly) with StandardScaler
  - Neural: MLPRegressor (small)
  - Dimensionality reduction: PCA+Ridge
  - Unsupervised-augmented: KMeansLabeler/DBSCANApproxLabeler/IsolationScore feeding Ridge
- run_model_comparison(df, reports_root): main routine; builds features; executes CV and holdout evaluation; writes CSVs and visualizations (PNG + experimental HTML via Plotly/Altair/Bokeh when possible).


## Catálogo de Visualizações (PNG e HTML interativo)

Esta seção cataloga todos os artefatos visuais produzidos pelo sistema, com caminhos, requisitos de dados, interpretação e fallback.

1) Matrizes de performance por família e métrica
- Arquivos (PNG):
  - reports/2025-08-15/plots/model_comparison/matrix_cv_wape.png
  - reports/2025-08-15/plots/model_comparison/matrix_cv_smape.png
  - reports/2025-08-15/plots/model_comparison/matrix_cv_mae.png
  - reports/2025-08-15/plots/model_comparison/matrix_cv_rmse.png
- HTML: não aplicável (apenas PNG nesta versão)
- Geração: requer res_df com colunas CV_* não vazias; modelos classificados por “family”.
- Interpretação: comparação horizontal por modelo dentro da família e entre famílias; cores mais “frias”/valores menores indicam melhor erro (dependendo do cmap).
- Fallback: se não houver métricas, a figura não é gerada; pipeline segue.

2) Melhor modelo por família (best-by-family)
- Arquivo (PNG): reports/2025-08-15/plots/model_comparison/best_by_family_{holdout_wape|cv_wape}.png
- HTML: não aplicável nesta versão
- Geração: ordena por HOLDOUT_WAPE (se existir) senão por CV_WAPE.
- Interpretação: indica o “melhor” de cada família (árvores, linear, kernel, neural, unsup-augmented) para seleção de shortlist.
- Fallback: não gera se a coluna de ordenação estiver ausente.

3) Distribuições de CV (box/violin) por família
- Arquivos (PNG):
  - reports/2025-08-15/plots/model_comparison/cv_distribution_box_{wape|smape|mae|rmse}.png
  - reports/2025-08-15/plots/model_comparison/cv_distribution_violin_{wape|smape|mae|rmse}.png
- HTML: não aplicável nesta versão
- Geração: requer CSVs de dobras por modelo: reports/2025-08-15/tables/cv_folds_{model}.csv
- Interpretação: avalia estabilidade/variabilidade por família; ideal para comparar robustez.
- Fallback: sem CSVs de dobras, não gera.

4) Testes de significância (Top-3 modelos, WAPE de CV)
- Arquivos:
  - Tabela: reports/2025-08-15/tables/cv_top_models_significance.csv
  - Gráfico (PNG): reports/2025-08-15/plots/model_comparison/cv_significance_top3.png
- HTML: não aplicável nesta versão
- Geração: requer distribuição de WAPE por dobra para ao menos 3 modelos; aplica Mann-Whitney (two-sided) e salva p-values.
- Interpretação: barras mostram p-values; linha vermelha em 0,05. p < 0,05 sugere diferença estatística.
- Fallback: sem dobras suficientes, não gera.

5) Comparação de importâncias/coeficientes (modelos interpretáveis)
- Arquivo (PNG): reports/2025-08-15/plots/model_comparison/feature_importance_comparison.png
- HTML: não aplicável nesta versão
- Geração: refit no treino completo para modelos com feature_importances_ (árvores) ou coef_ (Ridge/Lasso); normaliza por soma(|valor|).
- Interpretação: heatmap mostra quais atributos pesam mais em cada modelo, facilitando auditoria técnica.
- Fallback: se não houver modelos interpretáveis válidos, não gera.

6) Comparativos tradicionais (CV vs Holdout) e Uplift (já existentes)
- PNG:
  - reports/2025-08-15/plots/model_comparison/compare_{wape|smape|mae|rmse}.png
  - reports/2025-08-15/plots/model_comparison/uplift_vs_baseline_{baseline}.png
- HTML (quando bibliotecas instaladas):
  - reports/2025-08-15/plots/model_comparison/experimental/compare_{metric}_{plotly|altair|bokeh}.html
  - reports/2025-08-15/plots/model_comparison/experimental/uplift_vs_baseline_{baseline}_{plotly|altair|bokeh}.html
- Geração: requer métricas agregadas; os HTMLs são opcionais e criados sob try/except.
- Interpretação: contraste entre média de CV e holdout; uplift mostra ganho vs baseline.
- Fallback: se faltarem dados, PNG/HTML não são gerados, sem afetar o restante.

7) Segmentações (macro-região, receita)
- PNG: segmentation_wape_by_macroregiao.png, segmentation_tmmn_by_macroregiao.png, segmentation_wape_by_revenue_quintile.png
- HTML (quando disponível): versões *_plotly.html|*_altair.html|*_bokeh.html na pasta experimental
- Geração: requer melhor modelo ajustado e dados suficientes por segmento.
- Interpretação: aponta onde o modelo é mais/menos preciso por recorte de negócio.
- Fallback: ausência de dados segmentados impede a geração.

Requisitos e limitações (dataset curto)
- Com ~5 dias, é comum não haver dobras de CV ou holdout robusto por família; diversos artefatos acima não serão emitidos.
- Todos os blocos possuem fallback silencioso, preservando a execução do pipeline.

## Visualization System
- Matplotlib/Seaborn baseline PNGs (comparison CV vs holdout; uplift; segmentations; new matrices and distributions).
- Experimental interactive backends: Plotly, Altair, Bokeh HTML mirrors with graceful fallbacks.
- Enhanced comparative visuals (added):
  - Cross-model performance matrices per metric
  - Best-by-family bar chart
  - CV distribution box/violin per family
  - Mann-Whitney significance between top-3 models (CV WAPE); CSV + bar plot
  - Feature importance/coefficients comparison heatmap across interpretable models

## Error Handling & Fallbacks
- Try/except guards around modeling and plotting to keep pipeline robust under short datasets (5-day case).

---

# Unit Testing Preparation Roadmap

## Coverage Priorities
1. Leakage-safe transforms
   - _target_encode smoothing/back-off; fit only on training data.
   - Rolling/lag features ensure shift(1) precedes rolling.
2. Metrics
   - wape and smape numerical stability and edge cases (zeros, negatives, empty arrays).
3. Unsupervised augmenters
   - KMeansLabeler: deterministic labels for fixed random_state; shape/append correctness.
   - DBSCANApproxLabeler: label propagation via nearest neighbor; behavior when all labels=-1.
   - IsolationScore: score shape and monotonicity (more anomalous → higher score after negation).
4. Model grid
   - Presence of key families (tree-based, linear, kernel, neural, unsup-augmented).
   - Pipeline components include StandardScaler where needed.
5. Visualization hooks (smoke tests)
   - Functions run without raising exceptions on a tiny synthetic dataset.

## Edge Case Scenarios
- Empty dataset: functions return early and do not crash.
- Single-day data per loja: CV reduces gracefully; holdout skipped; outputs still written.
- Missing features/UFs: macro mapping handles unknowns; TE handles unseen categories via global mean.
- Constant target in fold: model training skipped or metrics NaN; pipeline continues.

## Expected I/O and Error Handling
- _target_encode: inputs are pd.Series; returns np.ndarray aligned to apply_cat; unseen categories → global mean.
- Augmenters: fit(X[,y]) then transform(X_new) → np.ndarray with one extra column; raises if fit not called.
- run_model_comparison: accepts cleaned df with required columns; writes CSVs/PNGs; returns dict with features and results_path; never raises on missing optional libs.

## Suggested Unit Tests (pytest-style)
- test_metrics_wape_smape_edge_cases()
- test_target_encode_smoothing_and_unseen()
- test_kmeans_labeler_fit_transform_shapes_and_labels()
- test_dbscan_labeler_fit_transform_propagation()
- test_isolation_score_appends_and_sign()
- test_model_grid_includes_families_and_pipelines()
- test_run_model_comparison_smoke_tiny_dataset(tmp_path)


