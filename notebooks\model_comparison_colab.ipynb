{"cells": [{"cell_type": "code", "execution_count": null, "id": "83842507", "metadata": {}, "outputs": [], "source": ["# [anti-id] helpers – escolher dimensão de negócio em vez de IDs\n", "SAFE_DIM_PRIORITY = ['UF','uf','Cidade','cidade','Tipo_PDV','tipo_pdv','Estado_Emp','estado_emp']\n", "def SAFE_DIM_OF(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in SAFE_DIM_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_PRIORITY[0]\n", "# Var global padrão – tenta inferir de df se existir, senão usa primeira opção\n", "try:\n", "    SAFE_DIM = SAFE_DIM_OF(df)\n", "except Exception:\n", "    SAFE_DIM = SAFE_DIM_PRIORITY[0]\n"]}, {"cell_type": "code", "execution_count": null, "id": "a61a73cb", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "code", "execution_count": null, "id": "7956ccf2", "metadata": {}, "outputs": [], "source": ["\n", "# [anti-id] addendum: funções auxiliares adicionais\n", "BUSINESS_ENTITY_PRIORITY = ['id_loja','id_cliente','id_vendedor','id_produto']\n", "def BUSINESS_ENTITY_DIM(df_like):\n", "    try:\n", "        cols = list(df_like.columns)\n", "    except Exception:\n", "        cols = []\n", "    for c in BUSINESS_ENTITY_PRIORITY:\n", "        if c in cols:\n", "            return c\n", "    return SAFE_DIM_OF(df_like)\n", "def UNIQUE_KEYS(*keys):\n", "    out = []\n", "    seen = set()\n", "    for k in keys:\n", "        if isinstance(k, (list, tuple)):\n", "            for x in k:\n", "                if x and x not in seen:\n", "                    seen.add(x); out.append(x)\n", "        else:\n", "            if k and k not in seen:\n", "                seen.add(k); out.append(k)\n", "    return out\n"]}, {"cell_type": "markdown", "id": "5b2824c3", "metadata": {}, "source": ["### [auto-doc] Etapa 1\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "35631ae0", "metadata": {}, "outputs": [], "source": ["# [auto-doc] estilo global\n", "import matplotlib as mpl, seaborn as sns\n", "import matplotlib.pyplot as plt\n", "sns.set_theme(style='whitegrid', context='notebook', palette='deep')\n", "mpl.rcParams.update({'figure.dpi': 150, 'axes.titlesize': 12, 'axes.labelsize': 11, 'legend.fontsize': 10})\n", "\n", "# [auto-doc] Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.title('<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> tí<PERSON>lo descritivo (ajustar)')\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "e1021c60", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "4e9e454e", "metadata": {}, "source": ["# Comparação de Modelos (Colab) — Chilli Beans\n", "\n", "Notebook de comparação de algoritmos com validação cruzada, tuning de hiperparâmetros e explicabilidade.\n", "\n", "- 3+ modelos candidatos\n", "- Grid/Random Search para tuning\n", "- Artefatos exportados para reports/2025-08-15/\n"]}, {"cell_type": "markdown", "id": "b12bdedd", "metadata": {}, "source": ["## 1. <PERSON><PERSON> <PERSON> (Colab)"]}, {"cell_type": "markdown", "id": "88033673", "metadata": {}, "source": ["### [auto-doc] Etapa 2\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f3adbfaa", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:04.240508Z", "iopub.status.busy": "2025-09-15T15:14:04.240508Z", "iopub.status.idle": "2025-09-15T15:14:04.250107Z", "shell.execute_reply": "2025-09-15T15:14:04.249571Z"}}, "outputs": [], "source": ["#@title Instalação de dependências (apenas Colab)\n", "import sys, subprocess\n", "IN_COLAB = 'google.colab' in sys.modules\n", "if IN_COLAB:\n", "    pkgs = ['pandas>=1.5.0','numpy>=1.21.0','scikit-learn>=1.1.0',\n", "            'seaborn>=0.12.0','matplotlib>=3.5.0','shap>=0.45.0']\n", "    subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-q'] + pkgs)\n"]}, {"cell_type": "markdown", "id": "81cc790a", "metadata": {}, "source": ["## 2. Carregamento de Dados e Detecção de Tarefa"]}, {"cell_type": "markdown", "id": "468f2b1d", "metadata": {}, "source": ["### [auto-doc] Etapa 3\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "2ed08c5d", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:04.253596Z", "iopub.status.busy": "2025-09-15T15:14:04.252624Z", "iopub.status.idle": "2025-09-15T15:14:07.528916Z", "shell.execute_reply": "2025-09-15T15:14:07.527880Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset: features_engineered_regional.csv | TARGET: valor | Tarefa: regressão | X shape: (4000, 182) | Modo: Local rápido\n"]}], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import StratifiedKFold, KFold, cross_validate, train_test_split, RandomizedSearchCV, GridSearchCV\n", "from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, average_precision_score,\n", "    mean_squared_error, r2_score, mean_absolute_error)\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression, LinearRegression\n", "from sklearn.svm import SVC, SVR\n", "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor\n", "from sklearn.neural_network import MLPClassifier, MLPRegressor\n", "import warnings; warnings.filterwarnings('ignore')\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed').exists(): BASE = Path('..')\n", "REPORTS = BASE/'reports'/'2025-08-15'\n", "PLOTS = REPORTS/'plots'/'model_comparison'\n", "TABLES = REPORTS/'tables'\n", "for d in [PLOTS, TABLES]: d.mkdir(parents=True, exist_ok=True)\n", "# Escolha robusta do dataset\n", "DATA = None\n", "cand = [BASE/'data'/'processed'/'features_engineered_regional.csv', BASE/'data'/'processed'/'features_engineered.csv']\n", "for c in cand:\n", "    if c.exists(): DATA = c; break\n", "assert DATA is not None, 'Dataset de features não encontrado em data/processed'\n", "df = pd.read_csv(DATA)\n", "# Downsample para estabilidade\n", "MAX_N = 5000 if IN_COLAB else 4000\n", "if len(df) > MAX_N: df = df.sample(MAX_N, random_state=42).reset_index(drop=True)\n", "# Heurística do TARGET\n", "if 'valor' in df.columns:\n", "    TARGET = 'valor'\n", "else:\n", "    cand_t = [c for c in df.columns if c.lower() in ('target','label','y','classe','class')]\n", "    TARGET = cand_t[0] if cand_t else df.columns[-1]\n", "y = df[TARGET]\n", "X = df.drop(columns=[TARGET])\n", "X = X.select_dtypes(include=['number'])\n", "is_classification = (pd.api.types.is_integer_dtype(y) and y.nunique()<=10) or (y.dtype=='object')\n", "print('Dataset:', DATA.name, '| TARGET:', TARGET, '| Tarefa:', 'classificação' if is_classification else 'regressão', '| X shape:', X.shape, '| Modo:', 'Colab' if IN_COLAB else 'Local rápido')\n"]}, {"cell_type": "markdown", "id": "7dbc1068", "metadata": {}, "source": ["## 3. Modelos Candidatos e Espaços de Hiperparâmetros"]}, {"cell_type": "markdown", "id": "71083d28", "metadata": {}, "source": ["### [auto-doc] Etapa 4\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "cd69a52d", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:07.532487Z", "iopub.status.busy": "2025-09-15T15:14:07.531510Z", "iopub.status.idle": "2025-09-15T15:14:07.543081Z", "shell.execute_reply": "2025-09-15T15:14:07.543081Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modelos definidos: ['LinReg', 'RF', 'SVR']\n"]}], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "np.random.seed(42)\n", "if is_classification:\n", "    models = {\n", "        'LogReg': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', LogisticRegression(max_iter=500))]),\n", "        'RF': RandomForestClassifier(n_estimators=200, random_state=42),\n", "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', SVC(kernel='rbf', probability=True, random_state=42))])\n", "    }\n", "    param_spaces = {\n", "        'LogReg': {'clf__C': [0.1, 1, 3, 10], 'clf__penalty': ['l2'], 'clf__solver': ['lbfgs']},\n", "        'RF': {'n_estimators': [200, 400, 800], 'max_depth': [None, 10, 20, 40], 'min_samples_leaf': [1, 2, 5]},\n", "        'SVM': {'clf__C': [0.5, 1, 2, 4], 'clf__gamma': ['scale', 0.1, 0.01, 0.001]}\n", "    }\n", "    cv = StratifiedKFold(n_splits=(5 if IN_COLAB else 3), shuffle=True, random_state=42)\n", "    primary_metric = 'f1_weighted'\n", "    scoring = {\n", "        'accuracy': 'accuracy',\n", "        'precision': 'precision_weighted',\n", "        'recall': 'recall_weighted',\n", "        'f1_weighted': 'f1_weighted',\n", "        'roc_auc_ovr': 'roc_auc_ovr'\n", "    }\n", "else:\n", "    models = {\n", "        'LinReg': LinearRegression(),\n", "        'RF': RandomForestRegressor(n_estimators=300, random_state=42),\n", "        'SVR': Pipeline([('sc', StandardScaler(with_mean=False)), ('svr', SVR(kernel='rbf'))])\n", "    }\n", "    param_spaces = {\n", "        'LinReg': {},\n", "        'RF': {'n_estimators': [300, 600, 900], 'max_depth': [None, 10, 20, 40], 'min_samples_leaf': [1, 2, 4]},\n", "        'SVR': {'svr__C': [1, 5, 10, 20], 'svr__gamma': ['scale', 0.1, 0.01], 'svr__epsilon': [0.1, 0.2, 0.5]}\n", "    }\n", "    cv = KFold(n_splits=(5 if IN_COLAB else 3), shuffle=True, random_state=42)\n", "    primary_metric = 'neg_root_mean_squared_error'\n", "    scoring = {'rmse': 'neg_root_mean_squared_error', 'mae': 'neg_mean_absolute_error', 'r2': 'r2'}\n", "print('Modelos definidos:', list(models.keys()))"]}, {"cell_type": "markdown", "id": "393bc365", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON> (baseline, sem tuning)"]}, {"cell_type": "markdown", "id": "0ac65842", "metadata": {}, "source": ["### [auto-doc] Etapa 5\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d98d53a9", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:07.546999Z", "iopub.status.busy": "2025-09-15T15:14:07.546999Z", "iopub.status.idle": "2025-09-15T15:14:39.424652Z", "shell.execute_reply": "2025-09-15T15:14:39.424652Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>rmse_mean</th>\n", "      <th>rmse_std</th>\n", "      <th>mae_mean</th>\n", "      <th>mae_std</th>\n", "      <th>r2_mean</th>\n", "      <th>r2_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>LinReg</td>\n", "      <td>-9.926238e+10</td>\n", "      <td>1.403782e+11</td>\n", "      <td>-4.709017e+09</td>\n", "      <td>6.659555e+09</td>\n", "      <td>-8.952922e+22</td>\n", "      <td>1.266134e+23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>RF</td>\n", "      <td>-1.983303e-02</td>\n", "      <td>1.207738e-02</td>\n", "      <td>-1.841847e-03</td>\n", "      <td>6.808157e-04</td>\n", "      <td>9.985341e-01</td>\n", "      <td>1.536601e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SVR</td>\n", "      <td>-4.772154e-01</td>\n", "      <td>1.627767e-02</td>\n", "      <td>-3.765842e-01</td>\n", "      <td>9.291691e-03</td>\n", "      <td>3.522458e-01</td>\n", "      <td>1.205085e-02</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    model     rmse_mean      rmse_std      mae_mean       mae_std  \\\n", "0  LinReg -9.926238e+10  1.403782e+11 -4.709017e+09  6.659555e+09   \n", "1      RF -1.983303e-02  1.207738e-02 -1.841847e-03  6.808157e-04   \n", "2     SVR -4.772154e-01  1.627767e-02 -3.765842e-01  9.291691e-03   \n", "\n", "        r2_mean        r2_std  \n", "0 -8.952922e+22  1.266134e+23  \n", "1  9.985341e-01  1.536601e-03  \n", "2  3.522458e-01  1.205085e-02  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "results = []\n", "for name, model in models.items():\n", "    cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n", "    row = {'model': name}\n", "    for k,v in cvres.items():\n", "        if k.startswith('test_'):\n", "            mname = k.replace('test_','')\n", "            row[mname+'_mean'] = float(np.mean(v))\n", "            row[mname+'_std'] = float(np.std(v))\n", "    results.append(row)\n", "res_df = pd.DataFrame(results)\n", "res_df.to_csv(TABLES / 'algorithm_ranking_baseline.csv', index=False)\n", "res_df"]}, {"cell_type": "markdown", "id": "3243b20a", "metadata": {}, "source": ["## 5. <PERSON><PERSON> Hiperparâmetros (Random/Grid Search)"]}, {"cell_type": "markdown", "id": "7794d583", "metadata": {}, "source": ["### [auto-doc] Etapa 6\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "078617b1", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:14:39.428564Z", "iopub.status.busy": "2025-09-15T15:14:39.427588Z", "iopub.status.idle": "2025-09-15T15:21:32.121386Z", "shell.execute_reply": "2025-09-15T15:21:32.120638Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>tuned</th>\n", "      <th>best_params</th>\n", "      <th>rmse_mean</th>\n", "      <th>rmse_std</th>\n", "      <th>mae_mean</th>\n", "      <th>mae_std</th>\n", "      <th>r2_mean</th>\n", "      <th>r2_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>LinReg</td>\n", "      <td>True</td>\n", "      <td>{}</td>\n", "      <td>-9.926238e+10</td>\n", "      <td>1.403782e+11</td>\n", "      <td>-4.709017e+09</td>\n", "      <td>6.659555e+09</td>\n", "      <td>-8.952922e+22</td>\n", "      <td>1.266134e+23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>RF</td>\n", "      <td>True</td>\n", "      <td>{'n_estimators': 300, 'min_samples_leaf': 2, '...</td>\n", "      <td>-2.228375e-02</td>\n", "      <td>1.141833e-02</td>\n", "      <td>-1.862493e-03</td>\n", "      <td>5.894504e-04</td>\n", "      <td>9.982742e-01</td>\n", "      <td>1.521322e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SVR</td>\n", "      <td>True</td>\n", "      <td>{'svr__gamma': 'scale', 'svr__epsilon': 0.2, '...</td>\n", "      <td>-2.548119e-01</td>\n", "      <td>7.939436e-03</td>\n", "      <td>-1.838654e-01</td>\n", "      <td>3.182500e-03</td>\n", "      <td>8.152574e-01</td>\n", "      <td>4.544397e-03</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    model  tuned                                        best_params  \\\n", "0  LinReg   True                                                 {}   \n", "1      RF   True  {'n_estimators': 300, 'min_samples_leaf': 2, '...   \n", "2     SVR   True  {'svr__gamma': 'scale', 'svr__epsilon': 0.2, '...   \n", "\n", "      rmse_mean      rmse_std      mae_mean       mae_std       r2_mean  \\\n", "0 -9.926238e+10  1.403782e+11 -4.709017e+09  6.659555e+09 -8.952922e+22   \n", "1 -2.228375e-02  1.141833e-02 -1.862493e-03  5.894504e-04  9.982742e-01   \n", "2 -2.548119e-01  7.939436e-03 -1.838654e-01  3.182500e-03  8.152574e-01   \n", "\n", "         r2_std  \n", "0  1.266134e+23  \n", "1  1.521322e-03  \n", "2  4.544397e-03  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["best_rows = []\n", "best_estimators = {}\n", "for name, model in models.items():\n", "    params = param_spaces.get(name, {})\n", "    if not params:\n", "        # sem hiperparâmetros — usa o próprio\n", "        estimator = model\n", "    else:\n", "        # estratégia mista: RandomizedSearch para grades maiores, <PERSON>rid pequeno para demais\n", "        n_combinations = 1\n", "        for v in params.values(): n_combinations *= len(v)\n", "        max_iter = (20 if IN_COLAB else 8)\n", "        if n_combinations > max_iter:\n", "            search = RandomizedSearchCV(model, params, n_iter=min(max_iter, n_combinations), scoring=primary_metric, cv=cv, random_state=42, n_jobs=1)\n", "        else:\n", "            search = GridSearchCV(model, params, scoring=primary_metric, cv=cv, n_jobs=1)\n", "        search.fit(X, y)\n", "        estimator = search.best_estimator_\n", "    # avaliar com cross_validate para métricas completas\n", "    cvres = cross_validate(estimator, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n", "    row = {'model': name, 'tuned': True}\n", "    try:\n", "        row['best_params'] = getattr(search, 'best_params_', {}) if params else {}\n", "    except Exception:\n", "        row['best_params'] = {}\n", "    for k,v in cvres.items():\n", "        if k.startswith('test_'):\n", "            mname = k.replace('test_','')\n", "            row[mname+'_mean'] = float(np.mean(v))\n", "            row[mname+'_std'] = float(np.std(v))\n", "    best_rows.append(row)\n", "    best_estimators[name] = estimator\n", "best_df = pd.DataFrame(best_rows)\n", "# expandir best_params em colunas texto\n", "best_df['best_params'] = best_df['best_params'].apply(lambda d: str(d))\n", "best_df.to_csv(TABLES / 'algorithm_ranking_tuned.csv', index=False)\n", "best_df\n"]}, {"cell_type": "markdown", "id": "30872536", "metadata": {}, "source": ["## 6. Visualizações de Performance"]}, {"cell_type": "markdown", "id": "a1ecc160", "metadata": {}, "source": ["### [auto-doc] Etapa 7\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "160a3daa", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:21:32.123501Z", "iopub.status.busy": "2025-09-15T15:21:32.123501Z", "iopub.status.idle": "2025-09-15T15:21:32.393042Z", "shell.execute_reply": "2025-09-15T15:21:32.392059Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "PLOTS.mkdir(parents=True, exist_ok=True)\n", "if is_classification:\n", "    plt.figure(figsize=(7,4));\n", "    sns.barplot(data=best_df, x='model', y='f1_weighted_mean');\n", "    plt.title('F1 (weighted) — Tuned'); plt.tight_layout(); plt.savefig(PLOTS/'bar_f1_tuned.png'); plt.show()\n", "else:\n", "    plt.figure(figsize=(7,4));\n", "    # lembrar: valores são negativos em 'neg_root_mean_squared_error' — converter\n", "    tmp = best_df.copy()\n", "    if 'rmse_mean' in tmp.columns: tmp['rmse_pos'] = tmp['rmse_mean'].abs()\n", "    sns.barplot(data=tmp, x='model', y='rmse_pos');\n", "    plt.ylabel('RMSE (médio)'); plt.title('RMSE — Tuned'); plt.tight_layout(); plt.savefig(PLOTS/'bar_rmse_tuned.png'); plt.show()\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')"]}, {"cell_type": "markdown", "id": "0c727779", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "e0b0cef6", "metadata": {}, "source": ["## 7. Explicabilidade (SHAP ou Importâncias)"]}, {"cell_type": "markdown", "id": "c173c826", "metadata": {}, "source": ["### [auto-doc] Etapa 8\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "b32436ba", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:21:32.396951Z", "iopub.status.busy": "2025-09-15T15:21:32.395972Z", "iopub.status.idle": "2025-09-15T15:21:43.250991Z", "shell.execute_reply": "2025-09-15T15:21:43.250991Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modelo para explicabilidade: RF\n"]}], "source": ["expl_model_name = 'RF' if 'RF' in best_estimators else list(best_estimators.keys())[0]\n", "expl = best_estimators[expl_model_name]\n", "print('Modelo para explicabilidade:', expl_model_name)\n", "# Ajuste final em amostra para SHAP (evitar custo alto)\n", "X_small = X.sample(min((2000 if IN_COLAB else 800), len(X)), random_state=42)\n", "y_small = y.loc[X_small.index]\n", "expl.fit(X_small, y_small)\n", "# Tentar SHAP; se falhar, usar feature_importances_\n", "try:\n", "    import shap\n", "    shap_explainer = None\n", "    if hasattr(expl, 'predict_proba') or isinstance(expl, RandomForestClassifier) or isinstance(expl, RandomForestRegressor):\n", "        shap_explainer = shap.TreeExplainer(expl)\n", "    if shap_explainer is not None:\n", "        shap_values = shap_explainer.shap_values(X_small)\n", "        plt.figure(figsize=(10,6))\n", "        shap.summary_plot(shap_values if isinstance(shap_values, list) else shap_values, X_small, show=False)\n", "        plt.tight_layout(); plt.savefig(PLOTS/'shap_summary.png', dpi=200); plt.close()\n", "    else:\n", "        raise RuntimeError('SHAP não suportado para este estimador; usando importâncias')\n", "except Exception as e:\n", "    print('Falha/indisponibilidade SHAP, usando importâncias:', e)\n", "    try:\n", "        importances = getattr(expl, 'feature_importances_', None)\n", "        if importances is not None:\n", "            s = pd.Series(importances, index=X_small.columns).sort_values(ascending=False).head(20)\n", "            plt.figure(figsize=(10,6)); s[::-1].plot(kind='barh'); plt.title('Importância de Features (Top 20)'); plt.tight_layout(); plt.savefig(PLOTS/'feature_importance.png', dpi=200); plt.close()\n", "    except Exception as e2:\n", "        print('Sem importâncias disponíveis:', e2)\n", "\n", "# Anotações automáticas – revise os textos para maior precisão\n", "import matplotlib.pyplot as plt\n", "plt.xlabel('Eixo X (ajustar)')\n", "plt.ylabel('<PERSON><PERSON><PERSON> Y (ajustar)')"]}, {"cell_type": "markdown", "id": "933dcf8a", "metadata": {}, "source": ["### [auto-doc] Interpretação\n", "\n", "**Interpretação:** Descreva padrões e insights observados.\n", "\n", "**Implicações:** Qual o impacto/uso para o negócio.\n", "\n", "**Próxi<PERSON> passos:** Como usar ou aprofundar esta análise.\n"]}, {"cell_type": "markdown", "id": "7d07ba51", "metadata": {}, "source": ["## 8. Recomendaç<PERSON>es de Modelo"]}, {"cell_type": "markdown", "id": "c788c6fc", "metadata": {}, "source": ["### [auto-doc] Etapa 9\n", "**Objetivo:** Descreva tecnicamente o que esta célula faz (processamento, análise, modelagem, etc.).\n", "\n", "**Contexto de negócio:** Explique por que esta etapa é relevante para decisões na Chilli Beans.\n", "\n", "**Dados:** [Input → Output esperado]\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1b148e27", "metadata": {"execution": {"iopub.execute_input": "2025-09-15T15:21:43.253926Z", "iopub.status.busy": "2025-09-15T15:21:43.253926Z", "iopub.status.idle": "2025-09-15T15:21:43.267814Z", "shell.execute_reply": "2025-09-15T15:21:43.267275Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>scenario</th>\n", "      <th>recommended</th>\n", "      <th>metric</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>menor erro (RMSE)</td>\n", "      <td>RF</td>\n", "      <td>rmse</td>\n", "      <td>0.022284</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            scenario recommended metric     value\n", "0  menor erro (RMSE)          RF   rmse  0.022284"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Nota: cé<PERSON>la extensa – inserir comentários objetivos sobre o que está sendo feito,\n", "# eventuais parâmetros relevantes e como interpretar o resultado.\n", "recs = []\n", "if is_classification:\n", "    best = best_df.sort_values('f1_weighted_mean', ascending=False).iloc[0]\n", "    recs.append({'scenario':'equilíbrio precisão/recall','recommended': best['model'],'metric': 'f1_weighted_mean', 'value': float(best['f1_weighted_mean'])})\n", "else:\n", "    # menor RMSE\n", "    tmp = best_df.copy(); tmp['rmse_pos'] = tmp['rmse_mean'].abs() if 'rmse_mean' in tmp.columns else np.nan\n", "    best = tmp.sort_values('rmse_pos', ascending=True).iloc[0]\n", "    recs.append({'scenario':'menor erro (RMSE)','recommended': best['model'], 'metric': 'rmse', 'value': float(best['rmse_pos'])})\n", "rec_df = pd.DataFrame(recs)\n", "rec_df.to_csv(TABLES/'model_recommendations_tuned.csv', index=False)\n", "rec_df"]}, {"cell_type": "markdown", "id": "5e8a195f", "metadata": {}, "source": ["## 9. <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "- Este notebook implementa comparação, tuning e explicabilidade com artefatos exportados para `reports/2025-08-15/`.\n", "- Para detalhes de métricas agregadas do pipeline atual, consulte também `reports/2025-08-15/tables/algorithm_ranking.csv`."]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}